import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../types';
import { PanelFilter } from '../FilterTypes';
import { Spin, Empty, Tabs, Switch, Tooltip, Button } from 'antd';
import Plot from 'react-plotly.js';
import { useSelector, useDispatch } from "react-redux";
import { addAnnotation, setActiveColumnName, setAnnotations } from '../../../../Redux/slices/annotationSlice';
import { addOperation, setActiveColumnName as setOperationActiveColumnName ,setOperations} from '../../../../Redux/slices/operationSlice';
import { postRequest } from '../../../../utils/apiHandler';
interface TimeSeriesPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string,data?:any) => void;
}

const TimeSeriesPanel: React.FC<TimeSeriesPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  onZoomSelection
}) => {

  console.log('original data in (Time Series Component)', data);
  console.log('data after filteredData in (Time Series Component)', filteredData);
  const containerRef = useRef<HTMLDivElement>(null);
  const [plotHeight, setPlotHeight] = useState(300);
  const [activeKey, setActiveKey] = useState("0");
  const [drawMode, setDrawMode] = useState<string>('select'); // Add state for tracking draw mode
  const [showFullData, setShowFullData] = useState<boolean>(false); // Toggle between filtered and full data view

  // Selection popup state
  const [selectionPopup, setSelectionPopup] = useState<{
    visible: boolean;
    x: number;
    y: number;
    selectionData: any;
  }>({
    visible: false,
    x: 0,
    y: 0,
    selectionData: null
  });
  const annotations = useSelector((state: any) => state.annotations);
  const selectedAnnotations = useSelector((state: any) => state.annotations.annotations);
  const operationsState = useSelector((state: any) => state.operations);
  const dispatch = useDispatch();
  const selectSystems = useSelector((state: any) => state.systems.systems);

  useEffect(() => {

    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        setPlotHeight(Math.floor(height * 0.8));
           setLayout((prev: any) => ({
      ...prev,
      height: Math.floor(height * 0.8)
    }));
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, []);


  // pass the intial default selected column name
  useEffect(()=>{
    if (columnTabs.length > 0) {
      dispatch(setActiveColumnName(columnTabs[parseInt(activeKey)].name))
      dispatch(setOperationActiveColumnName(columnTabs[parseInt(activeKey)].name))
    }
  },[])

  const fetchAnnotations = async () => {
    try {
      const dataToSend = {
        columnName: columnTabs[parseInt(activeKey)].name,
        systems: JSON.stringify(selectSystems[0]?.systems || []),
      };

      const response = await postRequest('/file/get-json-file', dataToSend);

      if (response?.data?.data) {
        const { anomaly, operation } = response.data.data;

        dispatch(
          setAnnotations([
            {
              columnName: columnTabs[parseInt(activeKey)].name,
              annotations: anomaly,
            },
          ])
        );
        dispatch(
          setOperations([
            {
              columnName: columnTabs[parseInt(activeKey)].name,
              operations: operation,
            },
          ])
        )
      }
    } catch (error) {
      console.error('Error fetching annotations:', error);
    }
  };

  useEffect(() => {
    if (columnTabs.length > 0 && columnTabs[parseInt(activeKey)]) {
      fetchAnnotations();
    }
  }, [activeKey]);

  const setZoomLayout = () => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      console.log('ZOOM LAYOUT - No valid column tabs or activeKey');
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];
    console.log('ZOOM LAYOUT - Selected Tab:', selectedTab.name);

    // Log the data we're working with
    console.log('ZOOM LAYOUT - Data available:', {
      filteredDataAvailable: filteredData && filteredData.length > 0,
      filteredDataLength: filteredData ? filteredData.length : 0,
      originalDataLength: data ? data.length : 0
    });

    // First, get extremes from filtered data (if available)
    const filteredExtremes = filteredData && filteredData.length > 0 ?
      getExtremes(filteredData, selectedTab.name) : null;

    // Also get extremes from original data as a fallback
    const originalExtremes = getExtremes(data, selectedTab.name);

    // Use filtered extremes if available, otherwise use original
    const extremes = filteredExtremes || originalExtremes;

    console.log('ZOOM LAYOUT - Extremes calculation:', {
      filteredExtremes: filteredExtremes,
      originalExtremes: originalExtremes,
      usingFilteredData: !!filteredExtremes
    });

    if (extremes) {
      console.log('ZOOM LAYOUT - Setting zoom layout based on data extremes:', extremes);

      // For x-axis, use date filter if available, otherwise use data extremes
      const xRange = dateFilter.startDate && dateFilter.endDate ?
        [dateFilter.startDate, dateFilter.endDate] :
        [extremes.DateTime.min, extremes.DateTime.max];

      // Calculate y-axis range with proper padding
      const yMin = extremes[selectedTab.name].min as any;
      const yMax = extremes[selectedTab.name].max as any;

      console.log('ZOOM LAYOUT - Y-axis range calculation:', {
        min: yMin,
        max: yMax,
        range: yMax - yMin
      });

      setLayout((prev: any) => {
        const newLayout = {
          ...prev,
          xaxis: {
            ...(prev?.xaxis || {}),
            range: xRange,
            fixedrange: false, // Allow x-axis zooming
          },
          yaxis: {
            ...(prev?.yaxis || {}),
            // Set the range but keep it fixed
            range: [yMin, yMax],
            fixedrange: true, // Lock y-axis when zooming
            autorange: false, // Disable autorange to use our custom range
          },
          dragmode: drawMode,
        };

        console.log('ZOOM LAYOUT - New layout being set:', {
          xaxisRange: newLayout.xaxis.range,
          yaxisRange: newLayout.yaxis.range,
          yaxisAutorange: newLayout.yaxis.autorange
        });

        return newLayout;
      });
    } else {
      console.log('ZOOM LAYOUT - No extremes available, cannot set layout');
    }
  }


  const getExtremes = (data: any[], key: string) => {
    console.log('GET EXTREMES - Starting calculation for key:', key);
    console.log('GET EXTREMES - Data sample:', data.slice(0, 3));

    if (!Array.isArray(data) || data.length === 0 || !key) {
      console.log('GET EXTREMES - Invalid data or key:', {
        isArray: Array.isArray(data),
        length: data?.length,
        key: key
      });
      return null;
    }

    let minKeyVal = Infinity;
    let maxKeyVal = -Infinity;
    let minDate = new Date(data[0].DateTime);
    let maxDate = new Date(data[0].DateTime);

    // Count valid values for debugging
    let validValueCount = 0;
    let nanValueCount = 0;

    for (let item of data) {
      const keyVal = parseFloat(item[key]);
      const date = new Date(item.DateTime);

      if (!isNaN(keyVal)) {
        minKeyVal = Math.min(minKeyVal, keyVal);
        maxKeyVal = Math.max(maxKeyVal, keyVal);
        validValueCount++;
      } else {
        nanValueCount++;
      }

      if (!isNaN(date.getTime())) {
        if (date < minDate) minDate = date;
        if (date > maxDate) maxDate = date;
      }
    }

    console.log('GET EXTREMES - Value counts:', {
      validValues: validValueCount,
      nanValues: nanValueCount,
      totalRows: data.length
    });

    console.log('GET EXTREMES - Raw extremes:', {
      minKeyVal: minKeyVal,
      maxKeyVal: maxKeyVal,
      minDate: minDate,
      maxDate: maxDate
    });

    // Add padding to the y-axis range to avoid stretched appearance
    // Calculate a padding of 20% of the range on both sides (increased from 10%)
    const range = maxKeyVal - minKeyVal;

    // Special case for when min and max are the same (flat line)
    if (range === 0 || range < 0.000001) {
      console.log('GET EXTREMES - Flat line detected, using special padding');
      // Use 50% of the value as padding, with a minimum of 1
      const flatLinePadding = Math.max(1, Math.abs(maxKeyVal) * 0.5);

      // Apply padding to min and max values
      const paddedMin = minKeyVal - flatLinePadding;
      const paddedMax = maxKeyVal + flatLinePadding;

      console.log('GET EXTREMES - Flat line padding:', {
        value: maxKeyVal,
        padding: flatLinePadding,
        paddedMin: paddedMin,
        paddedMax: paddedMax
      });

      const result = {
        [key]: {
          min: paddedMin,
          max: paddedMax,
          rawMin: minKeyVal,
          rawMax: maxKeyVal
        },
        DateTime: {
          min: minDate.toISOString(),
          max: maxDate.toISOString()
        }
      };

      console.log('GET EXTREMES - Final result for flat line:', result);
      return result;
    }

    const padding = range * 0.2;

    // If range is very small (near zero), use a fixed padding
    const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

    // Apply padding to min and max values
    const paddedMin = minKeyVal - effectivePadding;
    const paddedMax = maxKeyVal + effectivePadding;

    console.log('GET EXTREMES - Padding calculation:', {
      range: range,
      padding: padding,
      effectivePadding: effectivePadding,
      paddedMin: paddedMin,
      paddedMax: paddedMax
    });

    const result = {
      [key]: {
        min: paddedMin,
        max: paddedMax,
        rawMin: minKeyVal,
        rawMax: maxKeyVal
      },
      DateTime: {
        min: minDate.toISOString(),
        max: maxDate.toISOString()
      }
    };

    console.log('GET EXTREMES - Final result:', result);
    return result;
  };




  // Get annotations from Redux store - used in other parts of the component
  useSelector((state: any) => state.annotations);

  // Process data for the time series chart
  const plotData = useMemo(() => {
    console.log('TimeSeriesPanel - useMemo triggered');
    console.log('TimeSeriesPanel - panelFilters:', panelFilters);

    // Use filtered data or original data based on toggle state
    // Note: We'll still use the data for rendering even when it's filtered out by annotations
    const dataToProcess = showFullData ? data : (filteredData || data);

    console.log('TimeSeriesPanel - Data to process:', {
      usingFilteredData: showFullData ? false : !!filteredData,
      dataLength: dataToProcess?.length || 0
    });

    if (!dataToProcess || isLoading) return [];

    try {
      // Check if we have time series data
      if (!dataToProcess.timeSeries || !Array.isArray(dataToProcess.timeSeries)) {
        // Try to extract time series data from tabular data
        if (Array.isArray(dataToProcess) && dataToProcess.length > 0) {
          // Assuming first column is date/time and other columns are values
          const headers = Array.isArray(dataToProcess[0]) ? dataToProcess[0] : Object.keys(dataToProcess[0]);
          const rows = Array.isArray(dataToProcess[0]) ? dataToProcess.slice(1) : dataToProcess;

          // Find datetime column - exact match only (case insensitive)
          const dateColumnIndex = headers.findIndex(h =>
            h === 'DateTime'
          );

          if (dateColumnIndex >= 0) {
            // Create a series for each numeric column
            const series = [];

            for (let i = 0; i < headers.length; i++) {
              if (i !== dateColumnIndex) {
                const columnData = rows.map(row => {
                  const value = Array.isArray(row) ? row[i] : row[headers[i]];
                  return parseFloat(value);
                }).filter(val => !isNaN(val));

                if (columnData.length > 0) {
                  series.push({
                    name: headers[i],
                    data: columnData,
                    x: rows.map(row => Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]]),
                    y: columnData,
                    type: 'scatter',
                    mode: 'lines+markers',
                  });
                }
              }
            }

            return series;
          }
        }
        return [];
      }

      // If we have structured time series data
      if (dataToProcess.timeSeries.categories && dataToProcess.timeSeries.series) {
        return dataToProcess.timeSeries.series.map((series: any) => ({
          name: series.name,
          x: dataToProcess.timeSeries.categories,
          y: series.data,
          type: 'scatter',
          mode: 'lines+markers',
        }));
      }

      return [];
    } catch (error) {
      console.error('Error processing time series data:', error);
      return [];
    }
  }, [data, filteredData, isLoading, panelFilters, showFullData]);

  // Apply only column selection, not date filtering
  const filteredPlotData = useMemo(() => {
    let filtered = [...plotData];


    // Apply column selection if any
    if (selectedColumns.indices.length > 0 && selectedColumns.headers.length > 0) {
      // Filter series by selected column headers
      filtered = filtered.filter(series =>
        selectedColumns.headers.includes(series.name)
      );
    }

    return filtered;
  }, [plotData, selectedColumns]);

  // Group data by column for tabs - defined early to avoid circular dependencies
  const columnTabs = useMemo(() => {
    // If no plot data, return empty array
    if (!filteredPlotData || filteredPlotData.length === 0) {
      console.log('columnTabs - No filtered plot data available');
      return [];
    }

    console.log('columnTabs - Creating tabs for columns:',
      filteredPlotData.map(series => series.name));

    // Create a tab for each column
    return filteredPlotData.map(series => {
      // Calculate y-axis range for this series
      if (series.y && series.y.length > 0) {
        const validYValues = series.y.filter((val: any) => !isNaN(parseFloat(val)));
        if (validYValues.length > 0) {
          const minY = Math.min(...validYValues.map((val: any) => parseFloat(val)));
          const maxY = Math.max(...validYValues.map((val: any) => parseFloat(val)));
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log(`columnTabs - Flat line detected for ${series.name}, using padding:`, flatLinePadding);

            return {
              name: series.name,
              data: [series],
              yRange: [minY - flatLinePadding, maxY + flatLinePadding]
            };
          }

          // Normal case with range
          const padding = range * 0.2;
          const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

          console.log(`columnTabs - Y-axis range for ${series.name}:`, {
            min: minY,
            max: maxY,
            range: range,
            padding: effectivePadding,
            paddedMin: minY - effectivePadding,
            paddedMax: maxY + effectivePadding
          });

          return {
            name: series.name,
            data: [series],
            yRange: [minY - effectivePadding, maxY + effectivePadding]
          };
        }
      }

      // Fallback if no valid y values
      return {
        name: series.name,
        data: [series]
      };
    });
  }, [filteredPlotData]);

  // Set initial zoom range based on dateFilter and update when data changes
  useEffect(() => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];

    // Get extremes from filtered data if available
    const dataToUse = filteredData && filteredData.length > 0 ? filteredData : data;
    const extremes = getExtremes(dataToUse, selectedTab.name);

    if (!extremes) return;

    // If dateFilter is set, use it for x-axis range
    const xRange = dateFilter.startDate && dateFilter.endDate ?
      [dateFilter.startDate, dateFilter.endDate] :
      [extremes.DateTime.min, extremes.DateTime.max];

    console.log('Setting zoom range based on data and filters:', {
      xRange,
      yRange: [extremes[selectedTab.name].min, extremes[selectedTab.name].max]
    });

    setLayout((prev: any) => ({
      ...prev,
      xaxis: {
        ...(prev?.xaxis || {}),
        range: xRange,
        fixedrange: false, // Allow x-axis zooming
      },
      yaxis: {
        ...(prev?.yaxis || {}),
        // Set the range with our calculated values (with padding)
        range: [
          extremes[selectedTab.name].min,
          extremes[selectedTab.name].max
        ],
        fixedrange: true, // Lock y-axis when zooming
        autorange: false, // Disable autorange to use our custom range
      },
      dragmode: drawMode, // Ensure the current draw mode is maintained
    }));
  }, [filteredData, data, dateFilter, drawMode, activeKey, columnTabs]);

  // We'll remove the toggle functionality as requested

  // Configure layout for tabbed view
  const layout: any = {
    title: 'Time Series',
    autosize: true,
    height: plotHeight,
    margin: { l: 40, r: 60, b: 40, t: 0, pad: 2 },
    // shapes: [],
    xaxis: {
      title: 'Time',
      showgrid: true,
      zeroline: false,
    },
    yaxis: {
      title: 'Value',
      showgrid: true,
      zeroline: false,
      fixedrange: true, // Lock y-axis when zooming
    },
    showlegend: true,
    legend: {
      x: 0,
      y: 1,
      orientation: 'h'
    },
    hovermode: 'closest',
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
  };
  const [layout1, setLayout] = useState<any>(layout);
  // Create stacked chart layout for selected columns
  const createStackedChartLayout = (selectedSeries: any[]) => {
    // Handle case with no series
    if (!selectedSeries || selectedSeries.length === 0) {
      // Return a copy of the outer layout to avoid the block-scoped variable error
      return { ...layout };
    }

    // Create a new layout object for stacked charts
    const stackedLayout: any = {
      grid: {
        rows: selectedSeries.length,
        columns: 1,
        pattern: 'independent',
        roworder: 'top to bottom',
        rowgap: 0.10
      },
      title: 'Time Series for Selected Columns',
      showlegend: true,
      // Enable box selection
      dragmode: 'zoom',
      selectdirection: 'h', // Horizontal selection only
      legend: {
        x: 0,
        y: 1,
        orientation: 'h'
      },
      margin: { l: 70, r: 70, b: 40, t: 0, pad: 2 },
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
      hovermode: 'closest',
      height: Math.max(500, selectedSeries.length * 200),
    };

    // Create subplots array
    const subplots: string[][] = [];

    // Configure each y-axis and add to subplots
    selectedSeries.forEach((series, index) => {
      const yAxisId = index + 1;

      // Add to subplots array
      subplots.push([`xy${yAxisId}`]);

      // Calculate domain for proper stacking - divide the vertical space
      // Each subplot gets its own section of the vertical space
      const domainSize = 1.0 / selectedSeries.length;
      const domainStart = 1.0 - (index + 1) * domainSize;
      const domainEnd = 1.0 - index * domainSize;

      // Configure y-axis
      stackedLayout[`yaxis${yAxisId}`] = {
        title: {
          text: series.name,
          font: {
            size: 12,
            color: '#7f7f7f'
          }
        },
        showgrid: true,
        zeroline: false,
        // Set specific domain to ensure proper stacking
        domain: [domainStart, domainEnd - 0.05], // 0.05 gap between plots
        // Auto-scale each subplot
        autorange: true,
        // Add gridlines for better readability
        gridcolor: 'rgba(200, 200, 200, 0.2)',
        // Lock y-axis when zooming
        fixedrange: true,
      };
    });

    // Configure shared x-axis
    stackedLayout.xaxis = {
      title: {
        text: 'Time',
        font: {
          size: 14,
          color: '#7f7f7f'
        }
      },
      showgrid: true,
      zeroline: false,
      // Add gridlines for better readability
      gridcolor: 'rgba(200, 200, 200, 0.2)',
      // Auto-scale the x-axis
      autorange: true,
      // Format dates nicely
      type: 'date',
      tickformat: '%Y-%m-%d %H:%M:%S',
    };

    // Set subplots in grid
    stackedLayout.grid.subplots = subplots;

    return stackedLayout;
  };

  // Modify the plot data for stacked view
  const createStackedChartData = (selectedSeries: any[]) => {
    // Handle case with no series
    if (!selectedSeries || selectedSeries.length === 0) {
      return [];
    }

    // Assign different colors to each series
    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ];

    return selectedSeries.map((series, index) => ({
      ...series,
      yaxis: `y${index + 1}`,
      name: series.name,
      line: {
        color: colors[index % colors.length],
        width: 2
      },
      marker: {
        color: colors[index % colors.length],
        size: 6
      }
    }));
  };

  // Handle zoom selection events - now shows popup instead of direct action
  const handleZoomSelection = (eventData: any) => {
    if (!eventData) return;

    console.log('Selection Event Data:', eventData);

    // Make sure a range was selected
    const xRange = eventData?.range?.x;
    const yRange = eventData?.range?.y;
    if (!xRange || xRange.length !== 2 || !yRange || yRange.length !== 2) return;

    const [min, max] = xRange;
    const [ymin, ymax] = yRange;
    const selectedTab = columnTabs[parseInt(activeKey)].name;

    const selectionData = {
      x1: min,
      x2: max,
      y1: ymin,
      y2: ymax,
      selectedTab: selectedTab,
      xRange,
      yRange
    };

    // Calculate popup position based on selection center
    const plotElement = containerRef.current?.querySelector('.js-plotly-plot');
    if (plotElement) {
      const rect = plotElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // Show popup at center of selection
      setSelectionPopup({
        visible: true,
        x: centerX,
        y: centerY,
        selectionData
      });
    }
  };

  // Popup action handlers
  const handlePopupAction = (action: string) => {
    const { selectionData } = selectionPopup;
    if (!selectionData) return;

    const { x1, x2, y1, y2, selectedTab } = selectionData;

    switch (action) {
      case 'zoom':
        // Zoom to selection
        setLayout((prev: any) => ({
          ...prev,
          xaxis: {
            ...(prev?.xaxis || {}),
            range: [x1, x2],
          }
        }));

        // Call parent's zoom handler
        if (onZoomSelection) {
          onZoomSelection('date', x1, x2, 'time-series-panel', selectionData);
        }
        break;

      case 'anomaly':
        // Create annotation for anomaly
        const anomalyAnnotation = {
          type: 'rect',
          xref: 'x',
          yref: 'y',
          x0: x1,
          y0: y1,
          x1: x2,
          y1: y2,
          fillcolor: 'rgba(0, 0, 0, 0)',
          line: {
            color: 'rgba(255, 0, 0, 0.7)',
            width: 2,
            dash: 'solid'
          },
          shapeId: `anomaly-${Date.now()}`,
          label: {
            text: 'Anomaly'
          }
        };

        dispatch(addAnnotation({
          columnName: selectedTab,
          annotation: anomalyAnnotation
        }));
        break;

      case 'operation':
        // Create operation range
        const operationRange = {
          type: 'line',
          xref: 'paper',
          yref: 'y',
          y0: y1,
          y1: y2,
          line: {
            color: 'rgba(0, 128, 0, 0.7)',
            width: 2,
            dash: 'dash'
          },
          layer: 'below',
          operationId: `operation-${Date.now()}`,
          label: {
            text: `Operation Range ${y1.toFixed(2)} - ${y2.toFixed(2)}`
          }
        };

        dispatch(addOperation({
          columnName: selectedTab,
          operation: operationRange
        }));
        break;

      case 'filter':
        // Apply as filter - this would depend on your filtering logic
        if (onZoomSelection) {
          onZoomSelection('date', x1, x2, 'time-series-panel', { ...selectionData, isFilter: true });
        }
        break;
    }

    // Hide popup after action
    setSelectionPopup(prev => ({ ...prev, visible: false }));
  };

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectionPopup.visible) {
        const target = event.target as Element;
        if (!target.closest('.selection-popup')) {
          setSelectionPopup(prev => ({ ...prev, visible: false }));
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [selectionPopup.visible]);

  const handleRelayout = (event: any) => {
    const x1 = event['xaxis.range[0]'];
    const x2 = event['xaxis.range[1]'];
    const y1 = event['yaxis.range[0]'];
    const y2 = event['yaxis.range[1]'];

    // Check if dragmode was changed by Plotly internally
    if (event.dragmode && event.dragmode !== drawMode) {
      // Update our state to match Plotly's internal state
      setDrawMode(event.dragmode);
    }

    // Handle x-axis zoom even without y-axis changes
    if (x1 && x2) {
      const selectedTab = columnTabs[parseInt(activeKey)].name;
      // Get current y-axis range if y1/y2 not provided (since y-axis is fixed)
      const currentYRange = layout1.yaxis?.range || [];
      const effectiveY1 = y1 || currentYRange[0];
      const effectiveY2 = y2 || currentYRange[1];

      console.log('Zoomed Range:', { x1, x2, y1: effectiveY1, y2: effectiveY2, selectedTab});
      let data = { x1, x2, y1: effectiveY1, y2: effectiveY2, selectedTab}
      // You can use these values for further actions (e.g., data filtering)
      onZoomSelection?.('date', 1, 2, 'time-series-panel', data)
    }
    if (event?.shapes) {
      const currentColumnName = columnTabs[parseInt(activeKey)].name;

      // Get existing annotation shapes for comparison
      const prevShapeStrings = annotations.annotations
        .filter((shape: any) => shape.columnName === currentColumnName)[0]?.annotations
        .map((shape: any) => JSON.stringify({ x0: shape.x0, y0: shape.y0, x1: shape.x1, y1: shape.y1 })) || [];

      // Filter out operation lines (which are also shapes) to only process annotation rectangles
      const annotationShapes = event.shapes.filter((shape: any) =>
        shape.type !== 'line' && shape.layer !== 'below' && (!shape.line || shape.line.dash !== 'dash')
      );

      // Find new shapes that don't exist in our current annotations

      // Process new annotations

      const newShapesWithIds = annotationShapes
        .filter((shape: any) => {
          // Only consider rectangle shapes (annotations)
          if (shape.type !== 'rect') return false;

          const shapeString = JSON.stringify({ x0: shape.x0, y0: shape.y0, x1: shape.x1, y1: shape.y1 });
          return !prevShapeStrings?.includes(shapeString);
        })
        .map((shape: any, index: number) => {
          // We're using a fixed color for annotations

          return {
            ...shape,
            shapeId: `shape-${Date.now()}-${index}`,
            fillcolor: 'rgba(0, 0, 0, 0)', // Transparent fill (no color)
            line: {
              ...(shape.line || {}),
              color: 'rgba(255, 0, 0, 0.7)', // More opaque border
              width: 2
            },
            label: {
              ...(shape.label || {}),
              text: `shape-${index + 1}` // Add label text
            },
          };
        });

      if (newShapesWithIds.length > 0) {
        // Add each new annotation to the store
        newShapesWithIds.forEach((annotation: any) => {
          dispatch(addAnnotation({ columnName: currentColumnName, annotation }));
        });
      }
    }
  };


  // Combined useEffect for both annotations and operations to ensure proper rendering
  useEffect(() => {
    if (!columnTabs || columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) return;

    // Using fixed colors for operations and annotations

    // Get annotations for the current column
    const clonedShapes = JSON.parse(JSON.stringify(selectedAnnotations)); // deep copy
    const annotationData = clonedShapes.filter((res:any)=>res.columnName == columnTabs[parseInt(activeKey)].name);

    // Only include selected annotations that are NOT being used as filters
    // When an annotation is used as a filter, we don't want to show it on the graph
    const findSelectedAnnotation = annotationData[0]?.annotations.filter((res:any)=>
      res.selected === true && res.applyAsFilter !== true
    );

    console.log('findSelectedAnnotation :', findSelectedAnnotation);

    // Apply colors to annotation shapes
    let annotationShapes = [];
    if (findSelectedAnnotation) {
      annotationShapes = findSelectedAnnotation.map((annotation: any) => {
        // Using fixed color for annotations

        // Apply color to the annotation border only (no fill)
        return {
          ...annotation,
          fillcolor: 'rgba(0, 0, 0, 0)', // Transparent fill (no color)
          line: {
            ...(annotation.line || {}),
            color: 'rgba(255, 0, 0, 0.7)', // More opaque border
            width: 2
          }
        };
      });
    }

    // Get operation ranges for the current column
    const clonedOperations = JSON.parse(JSON.stringify(operationsState?.operations)); // deep copy
    const operationData = clonedOperations.filter((res:any)=>res.columnName == columnTabs[parseInt(activeKey)].name)[0]?.operations.filter((res:any)=>res.selected==true);

    // Create horizontal lines for operation ranges
    const operationLines: any[] = [];
    const operationAnnotations: any[] = [];

    if (operationData && operationData.length) {
      operationData.forEach((op: any, index: number) => {
        // Using fixed green color for operations
        const operationColor = 'rgba(0, 128, 0, 0.7)';

        // Add line for y0 (lower bound)
        operationLines.push({
          type: 'line',
          xref: 'paper',
          yref: 'y',
          x0: 0,
          y0: op.y0,
          x1: 1,
          y1: op.y0,
          line: {
            color: operationColor,
            width: 2,
            dash: 'dash'
          },
          layer: 'below'
        });

        // Add line for y1 (upper bound)
        operationLines.push({
          type: 'line',
          xref: 'paper',
          yref: 'y',
          x0: 0,
          y0: op.y1,
          x1: 1,
          y1: op.y1,
          line: {
            color: operationColor,
            width: 2,
            dash: 'dash'
          },
          layer: 'below'
        });

        // Add label annotation near y-axis for the upper bound
        operationAnnotations.push({
          x: 0.01, // Just right of the y-axis
          y: op.y1,
          xref: 'paper',
          yref: 'y',
          text: op.label?.text || `Operation ${index + 1}`,
          showarrow: false,
          font: {
            family: 'Arial',
            size: 12,
            color: operationColor
          },
          align: 'left',
          bgcolor: 'rgba(255, 255, 255, 0.7)',
          bordercolor: operationColor,
          borderwidth: 1,
          borderpad: 2
        });
      });
    }

    // Important: Always put annotation shapes after operation lines
    // This ensures annotations are rendered on top and their positions are preserved
    const allShapes = [
      ...operationLines,
      ...annotationShapes
    ];

    // Update the layout with all shapes at once and add operation annotations
    setLayout((prev: any) => ({
      ...prev,
      shapes: allShapes,
      annotations: operationAnnotations,
      dragmode: drawMode
    }));
  }, [selectedAnnotations,operationsState, activeKey, drawMode]);


  // We don't need activeTab anymore as we're using columnTabs[parseInt(activeKey)] directly

  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // Ensure we update the active column name in both slices
    if (columnTabs.length > 0 && columnTabs[parseInt(key)]) {
      const tab = columnTabs[parseInt(key)];
      const columnName = tab.name;
      dispatch(setActiveColumnName(columnName));
      dispatch(setOperationActiveColumnName(columnName));

      // Fetch annotations for the new tab
      fetchAnnotations();

      // Log detailed information about the data and ranges for debugging
      console.log('TAB CHANGE - Column Name:', columnName);
      console.log('TAB CHANGE - Tab Data:', tab);

      // Log filtered data for this column
      const filteredDataForColumn = filteredData && filteredData.length > 0 ?
        filteredData.map((row: any) => ({
          DateTime: row.DateTime,
          [columnName]: row[columnName]
        })) : [];

      console.log('TAB CHANGE - Filtered Data Sample (first 5 rows):',
        filteredDataForColumn.slice(0, 5));
      console.log('TAB CHANGE - Filtered Data Length:',
        filteredDataForColumn.length);

      // Calculate and log min/max values for this column
      if (filteredDataForColumn.length > 0) {
        const yValues = filteredDataForColumn
          .map((row: any) => parseFloat(row[columnName]))
          .filter((val: number) => !isNaN(val));

        if (yValues.length > 0) {
          const minY = Math.min(...yValues);
          const maxY = Math.max(...yValues);
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log('TAB CHANGE - Flat line detected, using special padding:', flatLinePadding);

            // Update layout with flat line padding
            setLayout((prev: any) => ({
              ...prev,
              yaxis: {
                ...prev.yaxis,
                range: [minY - flatLinePadding, maxY + flatLinePadding],
                autorange: false
              }
            }));

            return; // Skip the rest of the function
          }

          const padding = range * 0.2;
          const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;
          const paddedMin = minY - effectivePadding;
          const paddedMax = maxY + effectivePadding;

          console.log('TAB CHANGE - Y-Axis Values:', {
            rawMin: minY,
            rawMax: maxY,
            range: range,
            padding: effectivePadding,
            paddedMin: paddedMin,
            paddedMax: paddedMax
          });

          // Directly update the layout with the calculated range
          setLayout((prev: any) => ({
            ...prev,
            yaxis: {
              ...prev.yaxis,
              range: [paddedMin, paddedMax],
              autorange: false
            }
          }));

          // Log current layout settings after update
          setTimeout(() => {
            console.log('TAB CHANGE - Updated Layout:', {
              xaxisRange: layout1.xaxis?.range,
              yaxisRange: layout1.yaxis?.range,
              yaxisAutorange: layout1.yaxis?.autorange
            });
          }, 100);

          return; // Skip the rest of the function
        }
      }

      // If we have a pre-calculated yRange in the tab, use it
      if (tab.yRange) {
        console.log('TAB CHANGE - Using pre-calculated yRange:', tab.yRange);

        setLayout((prev: any) => ({
          ...prev,
          yaxis: {
            ...prev.yaxis,
            range: tab.yRange,
            autorange: false
          }
        }));

        return; // Skip the rest of the function
      }

      // Log current layout settings
      console.log('TAB CHANGE - Current Layout:', {
        xaxisRange: layout1.xaxis?.range,
        yaxisRange: layout1.yaxis?.range,
        yaxisAutorange: layout1.yaxis?.autorange
      });
    }

    // Use setTimeout to ensure the tab change is processed before updating the layout
    // Only run this if we didn't already set the layout above
    setTimeout(() => {
      setZoomLayout();

      // Log the layout after update
      setTimeout(() => {
        console.log('TAB CHANGE - Updated Layout (from setZoomLayout):', {
          xaxisRange: layout1.xaxis?.range,
          yaxisRange: layout1.yaxis?.range,
          yaxisAutorange: layout1.yaxis?.autorange
        });
      }, 100);
    }, 0);
  };

  // Early returns after all hooks are called
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading time series data..." />
      </div>
    );
  }

  if (!filteredPlotData || filteredPlotData.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No time series data available" />
      </div>
    );
  }

  // Determine if we should show stacked view - always show stacked view when columns are selected
  const shouldShowStackedView = selectedColumns.indices.length > 0 && filteredPlotData.length > 0;

  // if (shouldShowStackedView) {
    // Stacked chart view for selected columns
    const stackedLayout = createStackedChartLayout(filteredPlotData);
    const stackedData = createStackedChartData(filteredPlotData);

    return (
      <div className="time-series-panel h-[95%]" ref={containerRef}>
        {
          shouldShowStackedView ? (
          <>
          <div className="flex justify-between items-center mb-1 px-3 pt-1">
            <h3 className="text-base font-medium">Time Series ({filteredPlotData.length})</h3>
            <div className="flex items-center">
              <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                <Switch
                  checked={showFullData}
                  onChange={setShowFullData}
                  checkedChildren="Full Data"
                  unCheckedChildren="Filtered"
                  className="mr-2"
                  size="small"
                />
              </Tooltip>
            </div>
          </div>
        <div className="p-2 h-[calc(100%-30px)] overflow-auto">
          <div style={{ minHeight: `${Math.max(400, filteredPlotData.length * 150)}px` }}>
            <Plot
              data={stackedData as any}
              layout={{
                ...stackedLayout,
                dragmode: 'zoom',
                xaxis: {
                  ...stackedLayout.xaxis,
                  fixedrange: false, // Allow x-axis zooming
                },
                uirevision: 'true', // Add uirevision to maintain state during updates
              }}
              config={{
                scrollZoom: true,
                displaylogo: false,
                modeBarButtonsToRemove: [  // Keep only essential buttons
                  'zoom2d',
                  'lasso2d',
                  'autoScale2d',
                  'resetScale2d',
                  'hoverClosestCartesian',
                  'hoverCompareCartesian',
                  'toggleSpikelines',
                  'toImage'
                ],
                // Keep minimal modebar
                displayModeBar: true,
                responsive: true,
                toImageButtonOptions: {
                  format: 'png',
                  filename: 'time_series_plot',
                  height: 500,
                  width: 700,
                  scale: 2
                }
              }}
              onSelected={handleZoomSelection}
              onRelayout={handleRelayout}
              style={{ width: '100%', height: '100%' }}
              useResizeHandler={true}
            />
          </div>
        </div></>) :(<>
          <div className="flex justify-between items-center mb-1 px-3 pt-0">
            <h3 className="text-base font-medium">Time Series</h3>
            <div className="flex items-center">
              <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                <Switch
                  checked={showFullData}
                  onChange={setShowFullData}
                  checkedChildren="Full Data"
                  unCheckedChildren="Filtered"
                  className="mr-2"
                  size="small"
                />
              </Tooltip>
            </div>
          </div>
          <Tabs defaultActiveKey="0" style={{ height: 'calc(100% - 40px)' }} onChange={handleTabChange} className='[&_.ant-tabs-nav]:m-0'>
          {columnTabs.map((tab, index) => (
            <Tabs.TabPane tab={tab.name} key={index.toString()}>
              <div className="p-2 h-full">
                <Plot
                  data={tab.data as any}
                  layout={{
                    ...layout1,
                    title: `${tab.name}`,
                    dragmode: drawMode, // Use the current draw mode from state
                    showlegend: tab.data.length > 1,
                    xaxis: {
                      ...layout1.xaxis,
                      fixedrange: false, // Allow x-axis zooming
                    },
                    yaxis: {
                      ...layout1.yaxis,
                      // Use the calculated y-axis range from columnTabs if available
                      range: tab.yRange || layout1.yaxis?.range,
                      fixedrange: true, // Lock y-axis when zooming
                      // Force autorange to false and use our calculated range
                      autorange: false
                    },
                    uirevision: 'true',
                  } as any}
                  config={{
                    scrollZoom: true,
                    displaylogo: false,
                    modeBarButtonsToRemove: [  // Keep only essential buttons
                      'zoom2d',
                      'lasso2d',
                      'autoScale2d',
                      'resetScale2d',
                      'hoverClosestCartesian',
                      'hoverCompareCartesian',
                      'toggleSpikelines',
                      'toImage'
                    ],
                    // Keep minimal modebar
                    displayModeBar: true,
                    responsive: true,
                    toImageButtonOptions: {
                      format: 'png',
                      filename: 'time_series_plot',
                      height: 500,
                      width: 700,
                      scale: 2
                    }
                  }}
                  style={{ width: '100%', height: 'calc(100% - 10px)' }}
                  useResizeHandler={true}
                  onSelected={handleZoomSelection}
                  onRelayout={handleRelayout}
                />
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs></>)
        }

        {/* Selection Popup */}
        {selectionPopup.visible && (
          <div
            className="selection-popup"
            style={{
              position: 'fixed',
              left: selectionPopup.x - 100, // Center the popup
              top: selectionPopup.y - 80,
              zIndex: 1000,
              backgroundColor: 'white',
              border: '1px solid #ccc',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              padding: '12px',
              minWidth: '200px'
            }}
          >
            <div style={{ marginBottom: '8px', fontWeight: 'bold', fontSize: '14px' }}>
              What would you like to do with this selection?
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Button
                size="small"
                type="primary"
                onClick={() => handlePopupAction('zoom')}
                style={{ textAlign: 'left' }}
              >
                🔍 Zoom to Selection
              </Button>
              <Button
                size="small"
                onClick={() => handlePopupAction('anomaly')}
                style={{ textAlign: 'left' }}
              >
                ⚠️ Mark as Anomaly
              </Button>
              <Button
                size="small"
                onClick={() => handlePopupAction('operation')}
                style={{ textAlign: 'left' }}
              >
                ⚙️ Set Operation Range
              </Button>
              <Button
                size="small"
                onClick={() => handlePopupAction('filter')}
                style={{ textAlign: 'left' }}
              >
                🔽 Apply as Filter
              </Button>
              <Button
                size="small"
                onClick={() => setSelectionPopup(prev => ({ ...prev, visible: false }))}
                style={{ textAlign: 'left', marginTop: '4px' }}
              >
                ❌ Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  // } else {
  //   // Original tab-based view
  //   return (
  //     <div className="time-series-panel h-full">
  //       <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
  //         {columnTabs.map((tab, index) => (
  //           <Tabs.TabPane tab={tab.name} key={index.toString()}>
  //             <div className="p-4 h-full">
  //               <Plot
  //                 data={tab.data as any}
  //                 layout={{
  //                   ...layout,
  //                   title: `Time Series: ${tab.name}`,
  //                   showlegend: tab.data.length > 1
  //                 } as any}
  //                 config={config as any}
  //                 style={{ width: '100%', height: 'calc(100% - 20px)' }}
  //                 useResizeHandler={true}
  //               />
  //             </div>
  //           </Tabs.TabPane>
  //         ))}
  //       </Tabs>
  //     </div>
  //   );
  // }
};

export default TimeSeriesPanel;
