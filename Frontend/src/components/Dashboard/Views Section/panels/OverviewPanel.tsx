import React, { useMemo, useState, useEffect } from 'react';
import { ColumnSelection, DateFilter } from '../types';
import { Spin, Table, Empty, Typography, Checkbox } from 'antd';

interface OverviewPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
}

interface ColumnStatistics {
  name: string;
  mean: string;
  min: string;
  max: string;
  stdDev: string;
  missingValues: string;
  distinctValues: string;
}

const OverviewPanel: React.FC<OverviewPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  onColumnSelection,
  onDateFilterChange
}) => {
  // Track selected columns internally
  const [internalSelectedColumns, setInternalSelectedColumns] = useState<string[]>([]);

  // Initialize internal selected columns from props
  useEffect(() => {
    // If selectedColumns is empty, clear internal selection
    if (selectedColumns.indices.length === 0) {
      setInternalSelectedColumns([]);
      return;
    }

    if (data) {
      let headers: string[] = [];

      // Extract headers from data
      if (Array.isArray(data)) {
        if (data.length > 0) {
          headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        }
      } else if (data?.table && Array.isArray(data.table)) {
        if (data.table.length > 0) {
          headers = Array.isArray(data.table[0]) ? data.table[0].map(String) : Object.keys(data.table[0]);
        }
      }

      // Map indices to column names
      const selectedNames = selectedColumns.indices
        .map(idx => headers[idx])
        .filter(Boolean);

      setInternalSelectedColumns(selectedNames);
    }
  }, [data, selectedColumns]);

  // Handle column selection via checkbox
  const handleColumnSelection = (record: ColumnStatistics) => {
    // Get all headers from data
    let headers: string[] = [];

    if (Array.isArray(data)) {
      if (data.length > 0) {
        headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
      }
    } else if (data?.table && Array.isArray(data.table)) {
      if (data.table.length > 0) {
        headers = Array.isArray(data.table[0]) ? data.table[0].map(String) : Object.keys(data.table[0]);
      }
    }

    // Find the index of this column
    const columnIndex = headers.findIndex(header => header === record.name);
    if (columnIndex === -1) return;

    // Toggle selection
    let newSelectedColumns: string[];
    if (internalSelectedColumns.includes(record.name)) {
      // Remove from selection
      newSelectedColumns = internalSelectedColumns.filter(name => name !== record.name);
    } else {
      // Add to selection
      newSelectedColumns = [...internalSelectedColumns, record.name];
    }

    // Update internal state
    setInternalSelectedColumns(newSelectedColumns);

    // Notify parent component
    if (onColumnSelection) {
      const selectedIndices = newSelectedColumns
        .map(name => headers.findIndex(header => header === name))
        .filter(idx => idx !== -1);

      onColumnSelection(selectedIndices, newSelectedColumns);
    }
  };
  // Calculate statistics for each column
  const columnStatistics = useMemo(() => {
    // Use filteredData if available, otherwise use original data
    const dataToProcess = filteredData || data;

    if (!dataToProcess || isLoading) return [];

    try {
      // Handle different data formats
      let headers: string[] = [];
      let rows: any[] = [];

      // Extract headers and rows from data
      if (Array.isArray(dataToProcess)) {
        if (dataToProcess.length > 0) {
          headers = Array.isArray(dataToProcess[0]) ? dataToProcess[0].map(String) : Object.keys(dataToProcess[0]);
          rows = Array.isArray(dataToProcess[0]) ? dataToProcess.slice(1) : dataToProcess;
        }
      } else if (dataToProcess.table && Array.isArray(dataToProcess.table)) {
        if (dataToProcess.table.length > 0) {
          headers = Array.isArray(dataToProcess.table[0]) ? dataToProcess.table[0].map(String) : Object.keys(dataToProcess.table[0]);
          rows = Array.isArray(dataToProcess.table[0]) ? dataToProcess.table.slice(1) : dataToProcess.table;
        }
      }

      // Calculate statistics for each column
      const statistics: ColumnStatistics[] = [];

      // Important: OverviewPanel should NOT filter itself based on column selection
      // Always process all columns regardless of selectedColumns, but exclude date/time columns
      // Date/time columns are excluded as they are used for filtering and x-axis in graphs, not for statistical analysis
      const columnsToProcess = headers
        .map((name, index) => ({ index, name }))
        .filter(({ name }) => !name.toLowerCase().includes('datetime'));


      columnsToProcess.forEach(({ index, name }) => {
        // Extract values for this column
        const values = rows.map(row => {
          const value = Array.isArray(row) ? row[index] : row[name];
          return value !== undefined && value !== null ? value : null;
        });

        // Extract numeric values for calculations
        const numericValues = values
          .map(value => typeof value === 'string' ? parseFloat(value) : value)
          .filter(value => value !== null && !isNaN(value as number)) as number[];

        // Count missing values
        const missingCount = values.filter(value => value === null || value === undefined || value === '').length;

        // Count distinct values
        const distinctCount = new Set(values.filter(value => value !== null && value !== undefined && value !== '')).size;

        // Calculate statistics
        let mean = 'N/A';
        let min = 'N/A';
        let max = 'N/A';
        let stdDev = 'N/A';

        if (numericValues.length > 0) {
          // Calculate mean
          const sum = numericValues.reduce((acc, val) => acc + val, 0);
          const meanValue = sum / numericValues.length;
          mean = meanValue.toFixed(2);

          // Calculate min and max
          min = Math.min(...numericValues).toFixed(2);
          max = Math.max(...numericValues).toFixed(2);

          // Calculate standard deviation
          const squaredDifferences = numericValues.map(value => Math.pow(value - meanValue, 2));
          const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / numericValues.length;
          stdDev = Math.sqrt(variance).toFixed(2);
        }

        statistics.push({
          name,
          mean,
          min,
          max,
          stdDev,
          missingValues: missingCount.toString(),
          distinctValues: distinctCount.toString()
        });
      });

      return statistics;
    } catch (error) {
      console.error('Error calculating statistics:', error);
      return [];
    }
  }, [data, filteredData, selectedColumns, isLoading]);

  // Define table columns
  const tableColumns = [
    {
      title: 'Select',
      dataIndex: 'select',
      key: 'select',
      width: 70,
      render: (_: any, record: ColumnStatistics) => (
        <Checkbox
          checked={internalSelectedColumns.includes(record.name)}
          onChange={() => handleColumnSelection(record)}
        />
      ),
    },
    {
      title: 'Column Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => a.name.localeCompare(b.name),
    },
    {
      title: 'Mean',
      dataIndex: 'mean',
      key: 'mean',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.mean);
        const bVal = parseFloat(b.mean);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Min',
      dataIndex: 'min',
      key: 'min',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.min);
        const bVal = parseFloat(b.min);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Max',
      dataIndex: 'max',
      key: 'max',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.max);
        const bVal = parseFloat(b.max);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Std Dev',
      dataIndex: 'stdDev',
      key: 'stdDev',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.stdDev);
        const bVal = parseFloat(b.stdDev);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Missing Values',
      dataIndex: 'missingValues',
      key: 'missingValues',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => parseInt(a.missingValues) - parseInt(b.missingValues),
    },
    {
      title: 'Distinct Values',
      dataIndex: 'distinctValues',
      key: 'distinctValues',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => parseInt(a.distinctValues) - parseInt(b.distinctValues),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Calculating statistics..." />
      </div>
    );
  }

  if (!columnStatistics || columnStatistics.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No data available for statistics" />
      </div>
    );
  }

  return (
    <div className="overview-panel h-full p-4 flex flex-col">
      <Typography.Title level={4} style={{ marginBottom: 16 }}>Data Overview</Typography.Title>
      <div className="flex-1 overflow-hidden">
        <Table
          dataSource={columnStatistics}
          columns={tableColumns}
          rowKey="name"
          pagination={false}
          size="small"
          bordered
          scroll={{
            y: 'calc(100vh - 200px)', // Adjust height based on your layout
            x: 'max-content' // Enable horizontal scroll if needed
          }}
          style={{ height: '100%' }}
        />
      </div>
    </div>
  );
};

export default OverviewPanel;
