import React, { useState, useRef } from 'react';
import { ComponentType, GridItemData, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import TimeSeriesPanel from './panels/TimeSeriesPanel';
import OverviewPanel from './panels/OverviewPanel';
import HistogramPanel from './panels/HistogramPanel';
import DataTablePanel from './panels/DataTablePanel';
import { CloseOutlined, ExpandOutlined } from '@ant-design/icons';
import PanelOptionsMenu from './PanelOptionsMenu';
import FullScreenModal from './FullScreenModal';

interface GridItemProps {
  file_id: string;
  data: GridItemData;
  fileData: any;
  filteredData?: any; // Pre-filtered data
  onRemove: (id: string) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter) => void;
  onRemoveFilter?: (filterId: string) => void;
}

const GridItem: React.FC<GridItemProps> = ({
  data,
  fileData,
  filteredData,
  file_id,
  onRemove,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = [],
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onAddFilter,
  onRemoveFilter,
}) => {
  // State for panel display
  const [isFullScreen, setIsFullScreen] = useState(false);
  const panelContentRef = useRef<HTMLDivElement>(null);

  // For OverviewPanel, we don't apply column filters to itself
  // const effectiveColumnSelection = isOverviewPanel
  //   ? { indices: [], headers: [] } // Empty selection for OverviewPanel
  //   : selectedColumns; // Use the global selection for other panels

  const effectiveColumnSelection =  selectedColumns; //Directly passed selectedColumns as checkboes were not reflectinng in the overview panel

  const renderComponent = () => {
    switch (data.type) {
      case ComponentType.TimeSeriesPanel:
        return (
          <TimeSeriesPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      case ComponentType.OverviewPanel:
        return (
          <OverviewPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      case ComponentType.HistogramPanel:
        return (
          <HistogramPanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      case ComponentType.DataTablePanel:
        return (
          <DataTablePanel
            data={fileData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter}
          />
        );
      default:
        return <div>Unknown component type</div>;
    }
  };

  // const handleFullscreenToggle = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   setIsFullscreen(!isFullscreen);
  // };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Remove button clicked for:', data.id);
    onRemove(data.id);
  };

  // Create a configuration object for the panel options menu
  const getPanelConfiguration = () => {
    return {
      panelFilters:{
        selectedColumns,
        dateFilter}
    };
  };

  return (
    <>
      <div className="grid-item">
        <div className="grid-item-header">
          <div className="grid-item-title drag-handle">{data.title}</div>
          <div className="grid-item-controls no-drag">
            <button
              className="grid-item-control"
              title="Expand panel"
              onClick={() => setIsFullScreen(true)}
            >
              <ExpandOutlined />
            </button>
            <PanelOptionsMenu
              panelType={data.type}
              panelRef={panelContentRef}
              panelTitle={data.title}
              configuration={getPanelConfiguration()}
              fileId={file_id}
            />
            <button
              className="grid-item-control"
              title="Remove panel"
              onClick={handleRemove}
            >
              <CloseOutlined />
            </button>
          </div>
        </div>
        <div className="grid-item-content no-drag"  ref={panelContentRef}>{renderComponent()}</div>
      </div>

      {/* Full Screen Modal */}
      <FullScreenModal
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        title={data.title}
      >
        <div className="full-screen-panel-content" style={{ width: '100%', height: '100%' }}>
          {/* Create a new instance of the component for full screen view */}
          {renderComponent()}
        </div>
      </FullScreenModal>
    </>
  );
};

export default GridItem;
